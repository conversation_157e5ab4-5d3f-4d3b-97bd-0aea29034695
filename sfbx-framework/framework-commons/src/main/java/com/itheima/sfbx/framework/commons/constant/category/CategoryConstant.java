package com.itheima.sfbx.framework.commons.constant.category;

import com.itheima.sfbx.framework.commons.constant.basic.SuperConstant;

/**
 * CategoryConstant
 *
 * @author: wgl
 * @describe: 分类常量
 * @date: 2022/12/28 10:10
 */
public class CategoryConstant extends SuperConstant {

    //常量:首页展示
    public static final String SHOW_INDEX_STATE_0 = "0";

    //常量:首页不展示
    public static final String SHOW_INDEX_STATE_1 = "1";


    //分类类型：0推荐分类
    public static final String RECOMMEND_TYPE = "0";

    //分类类型：1产品分类
    public static final String PRODUCT_TYPE = "1";

}