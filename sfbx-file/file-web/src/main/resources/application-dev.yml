#spring相关配置
spring:
  cache:
    default-company_no: *********
      #忽略商户号表
    ignore-company-tables:
      - undo_log
    #忽略数据权限表
    ignore-data-security-tables:
      - tab_file
      - tab_file_part
      - undo_log
  cloud:
    alicloud:
      access-key: LTAI4G2g6WqYBZyZMovev1pd
      secret-key: ******************************
      oss:
        bucket-name: itheima-sh-oss
        endpoint: oss-cn-shanghai.aliyuncs.com
    qiniu:
      accessKey: pFcOu02W6CdSqjY6qmRH1jz--pwfQEVyuM4RkBGC
      secretKey: z4CqmQb9LYD3AiuViX3ADk3r3-iOAm9yQePGP6Hh
      kodo:
        region: z1
        bucket-name: wisdom-static-images
        endpoint: rbfw15zs0.hd-bkt.clouddn.com
  #数据源配置
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: root
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
  #因为默认会尝试连接localhost:5672
  rabbitmq:
    host: localhost   #rabbitmq 服务器的地址
    port: 5672           #rabbitmq 服务器端口
    username: guest       #rabbitmq 用户名
    password: guest       #rabbitmq 密码
    virtual-host: /       #虚拟路径

#mybatis配置
mybatis-plus:
  # MyBaits 别名包扫描路径，通过该属性可以给包中的类注册别名
  type-aliases-package: com.itheima.sfbx.*.pojo
  # 该配置请和 typeAliasesPackage 一起使用，如果配置了该属性，则仅仅会扫描路径下以该类作为父类的域对象 。
  type-aliases-super-type: com.itheima.sfbx.framework.mybatisplus.basic.BasePojo
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-statement-timeout: 60
    default-fetch-size: 100
  default-company-no: *********
  #忽略商户号表
  ignore-company-tables:
    - undo_log
  #忽略数据权限表
  ignore-data-security-tables:
    - tab_file
    - tab_file_part
    - undo_log

itheima:
  framework:
    swagger:
      swagger-path: com.itheima.sfbx.file.web
      title: 基础服务
      description: 文件服务
      contact-name: 上海黑马
      contact-url: www.itheima.com
      contact-email: <EMAIL>

# OSS配置（用于测试，实际配置应该从Nacos获取）
sfbx:
  oss:
    aliyun:
      access-key-id: ${spring.cloud.alicloud.access-key}
      access-key-secret: ${spring.cloud.alicloud.secret-key}
      endpoint: ${spring.cloud.alicloud.oss.endpoint}
      bucket-name: ${spring.cloud.alicloud.oss.bucket-name}

management:
  health:
    rabbit:
      enabled: false
  endpoints:
    web:
      exposure:
        include: health,info
