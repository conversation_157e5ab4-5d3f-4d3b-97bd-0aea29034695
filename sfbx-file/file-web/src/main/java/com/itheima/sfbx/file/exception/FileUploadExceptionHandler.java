package com.itheima.sfbx.file.exception;

import com.itheima.sfbx.framework.commons.basic.ResponseResult;
import com.itheima.sfbx.framework.commons.utils.ResponseResultBuild;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import java.io.UncheckedIOException;

/**
 * @Description 文件上传异常处理器
 */
@Slf4j
@RestControllerAdvice
public class FileUploadExceptionHandler {

    /**
     * 处理文件大小超过限制异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseResult<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件大小超过限制: {}", e.getMessage());
        return ResponseResultBuild.failedBuild("文件大小超过限制，请选择较小的文件");
    }

    /**
     * 处理文件上传异常
     */
    @ExceptionHandler(MultipartException.class)
    public ResponseResult<String> handleMultipartException(MultipartException e) {
        log.error("文件上传异常: {}", e.getMessage());
        return ResponseResultBuild.failedBuild("文件上传失败，请重试");
    }

    /**
     * 处理IO异常（包括临时文件删除失败）
     */
    @ExceptionHandler(UncheckedIOException.class)
    public ResponseResult<String> handleUncheckedIOException(UncheckedIOException e) {
        // 如果是临时文件删除失败，不影响业务流程
        if (e.getMessage() != null && e.getMessage().contains("Cannot delete")) {
            log.warn("临时文件删除失败，但不影响业务流程: {}", e.getMessage());
            // 返回成功，因为文件上传实际上是成功的
            return ResponseResultBuild.successBuild("文件上传成功");
        }

        log.error("IO异常: {}", e.getMessage());
        return ResponseResultBuild.failedBuild("文件处理失败");
    }
}
