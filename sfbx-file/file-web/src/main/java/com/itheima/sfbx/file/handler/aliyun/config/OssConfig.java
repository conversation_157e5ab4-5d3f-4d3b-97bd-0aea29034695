package com.itheima.sfbx.file.handler.aliyun.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.itheima.sfbx.file.handler.aliyun.properties.OssAliyunConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description 阿里云OSS配置类
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(OssAliyunConfigProperties.class)
public class OssConfig {

    @Autowired(required = false)
    private OssAliyunConfigProperties ossAliyunConfigProperties;

    /**
     * 创建OSS客户端，配置连接超时参数
     * 只有在配置完整时才创建真实的OSS客户端
     */
    @Bean
    @ConditionalOnProperty(name = {"sfbx.oss.aliyun.access-key-id", "sfbx.oss.aliyun.access-key-secret", "sfbx.oss.aliyun.endpoint"})
    public OSS ossClient() {
        try {
            String accessKeyId = ossAliyunConfigProperties.getAccessKeyId();
            String accessKeySecret = ossAliyunConfigProperties.getAccessKeySecret();
            String endpoint = ossAliyunConfigProperties.getEndpoint();

            // 创建ClientConfiguration实例，用于设置客户端参数
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();

            // 设置连接超时时间（毫秒）
            conf.setConnectionTimeout(30000); // 30秒

            // 设置Socket超时时间（毫秒）
            conf.setSocketTimeout(60000); // 60秒

            // 设置连接请求超时时间（毫秒）
            conf.setConnectionRequestTimeout(10000); // 10秒

            // 设置最大连接数
            conf.setMaxConnections(200);

            // 设置最大错误重试次数
            conf.setMaxErrorRetry(3);

            // 设置是否支持CNAME
            conf.setSupportCname(false);

            // 设置是否开启二级域名的访问方式
            conf.setSLDEnabled(false);

            // 设置协议（HTTP/HTTPS）
            conf.setProtocol(com.aliyun.oss.common.comm.Protocol.HTTP);

            // 设置用户代理
            conf.setUserAgent("sfbx-file-service");

            log.info("OSS客户端配置 - 连接超时: {}ms, Socket超时: {}ms, 最大连接数: {}",
                    conf.getConnectionTimeout(), conf.getSocketTimeout(), conf.getMaxConnections());

            // 创建OSSClient实例
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, conf);

            log.info("OSS客户端创建成功，endpoint: {}", endpoint);
            return ossClient;

        } catch (Exception e) {
            log.error("创建OSS客户端失败: {}", e.getMessage(), e);
            throw new RuntimeException("OSS客户端初始化失败", e);
        }
    }
}
