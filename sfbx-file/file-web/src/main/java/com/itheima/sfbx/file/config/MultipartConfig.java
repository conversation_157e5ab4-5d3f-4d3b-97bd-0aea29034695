package com.itheima.sfbx.file.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import javax.servlet.MultipartConfigElement;

/**
 * @Description 文件上传配置类
 */
@Slf4j
@Configuration
public class MultipartConfig {

    /**
     * 配置文件上传解析器
     */
    @Bean
    public MultipartResolver multipartResolver() {
        StandardServletMultipartResolver resolver = new StandardServletMultipartResolver() {
            @Override
            public void cleanupMultipart(org.springframework.web.multipart.MultipartHttpServletRequest request) {
                try {
                    super.cleanupMultipart(request);
                } catch (Exception e) {
                    // 忽略临时文件删除失败的异常，避免影响正常业务流程
                    log.warn("清理临时文件失败，但不影响业务流程: {}", e.getMessage());
                }
            }
        };
        
        // 设置是否延迟解析文件
        resolver.setResolveLazily(true);
        
        return resolver;
    }

    /**
     * 配置文件上传参数
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小
        factory.setMaxFileSize(DataSize.ofMegabytes(100)); // 100MB
        
        // 设置总上传数据最大大小
        factory.setMaxRequestSize(DataSize.ofMegabytes(500)); // 500MB
        
        // 设置内存临界值，超过后将产生临时文件并存储于临时目录中
        factory.setFileSizeThreshold(DataSize.ofKilobytes(512)); // 512KB
        
        // 设置临时文件存储位置
        String tempDir = System.getProperty("java.io.tmpdir");
        factory.setLocation(tempDir);
        
        log.info("文件上传配置 - 最大文件大小: 100MB, 最大请求大小: 500MB, 临时目录: {}", tempDir);
        
        return factory.createMultipartConfig();
    }
}
