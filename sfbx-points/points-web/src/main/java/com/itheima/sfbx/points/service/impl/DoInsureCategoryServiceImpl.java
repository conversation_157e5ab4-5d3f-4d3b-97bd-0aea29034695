package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureCategoryMapper;
import com.itheima.sfbx.points.pojo.DoInsureCategory;
import com.itheima.sfbx.points.service.IDoInsureCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日投保分类明细服务实现类
 */
@Slf4j
@Service
public class DoInsureCategoryServiceImpl extends ServiceImpl<DoInsureCategoryMapper, DoInsureCategory> implements IDoInsureCategoryService {

}
