package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DnuCityMapper;
import com.itheima.sfbx.points.mapper.DnuMapper;
import com.itheima.sfbx.points.pojo.Dnu;
import com.itheima.sfbx.points.pojo.DnuCity;
import com.itheima.sfbx.points.service.IDnuCityService;
import com.itheima.sfbx.points.service.IDnuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description：新增用户所属城市服务类接口实现
 */
@Slf4j
@Service
public class DnuCityServiceImpl extends ServiceImpl<DnuCityMapper, DnuCity> implements IDnuCityService {

}
