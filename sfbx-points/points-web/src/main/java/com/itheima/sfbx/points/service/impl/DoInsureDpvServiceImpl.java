package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureDpvMapper;
import com.itheima.sfbx.points.pojo.DoInsureDpv;
import com.itheima.sfbx.points.service.IDoInsureDpvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日投保访问页面量服务实现类
 */
@Slf4j
@Service
public class DoInsureDpvServiceImpl extends ServiceImpl<DoInsureDpvMapper, DoInsureDpv> implements IDoInsureDpvService {

}
