package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureConversionDpvMapper;
import com.itheima.sfbx.points.pojo.DoInsureConversionDpv;
import com.itheima.sfbx.points.service.IDoInsureConversionDpvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：投保转换率服务实现类
 */
@Slf4j
@Service
public class DoInsureConversionDpvServiceImpl extends ServiceImpl<DoInsureConversionDpvMapper, DoInsureConversionDpv> implements IDoInsureConversionDpvService {

}
