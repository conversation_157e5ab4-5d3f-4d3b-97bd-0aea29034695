package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureCityDuvMapper;
import com.itheima.sfbx.points.pojo.DoInsureCityDuv;
import com.itheima.sfbx.points.service.IDoInsureCityDuvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：城市日投保用户访问数服务实现类
 */
@Slf4j
@Service
public class DoInsureCityDuvServiceImpl extends ServiceImpl<DoInsureCityDuvMapper, DoInsureCityDuv> implements IDoInsureCityDuvService {

}
