package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureFailDpvMapper;
import com.itheima.sfbx.points.pojo.DoInsureFailDpv;
import com.itheima.sfbx.points.service.IDoInsureFailDpvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日投保提交失败页面量服务实现类
 */
@Slf4j
@Service
public class DoInsureFailDpvServiceImpl extends ServiceImpl<DoInsureFailDpvMapper, DoInsureFailDpv> implements IDoInsureFailDpvService {

}
