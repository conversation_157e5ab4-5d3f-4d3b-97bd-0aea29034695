<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.itheima.sfbx</groupId>
    <artifactId>sfbx-cloud</artifactId>
    <version>2.0-SNAPSHOT</version>
    <modules>
        <!--数据字典模块-->
        <module>sfbx-dict</module>
        <!--基础骨架模块-->
        <module>sfbx-framework</module>
        <!--文件处理模块-->
        <module>sfbx-file</module>
        <!--埋点处理模块-->
        <module>sfbx-points</module>
        <!--交易处理模块-->
        <module>sfbx-trade</module>
        <!--权限处理模块-->
        <module>sfbx-security</module>
        <!--短信处理模块-->
        <module>sfbx-sms</module>
        <!--任务监听处理模块-->
        <module>sfbx-task</module>
        <!--保险业务模块-->
        <module>sfbx-insurance</module>
        <module>sfbx-gateway</module>
        <module>sfbx-apache-httpclient</module>
        <!--规则引擎模块-->
        <module>sfbx-rule</module>

    </modules>
    <packaging>pom</packaging>

    <name>sfbx-cloud</name>
    <url>http://www.example.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--easy.version-->
        <sfbx-cloud.version>2.0-SNAPSHOT</sfbx-cloud.version>
        <!--spring-cloud版本-->
        <spring-cloud.version>2021.0.6</spring-cloud.version>
        <!--spring-cloud-alibaba版本-->
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>
        <!--spring-cloud-stream版本-->
        <stream-rabbit.version>3.1.0</stream-rabbit.version>
        <!--spring-boot版本-->
        <spring.boot.version>2.7.10</spring.boot.version>
        <!--mybatis-plus版本-->
        <mybatis-plus-boot-starter.version>3.4.0</mybatis-plus-boot-starter.version>
        <!--druid的springboot版本配置-->
        <druid-spring-boot-starter>1.1.20</druid-spring-boot-starter>
        <!--druid版本配置-->
        <druid.version>1.1.22</druid.version>
        <!--knife4j版本支持-->
        <knife4j.version>3.0.3</knife4j.version>
        <!--orika 拷贝工具 -->
        <orika-core.version>1.5.4</orika-core.version>
        <!--hutool工具 -->
        <hutool.version>5.8.10</hutool.version>
        <!--lang3-->
        <commons.lang3.version>3.8.1</commons.lang3.version>
        <!--kryo-->
        <kryo.version>4.0.2</kryo.version>
        <!--阿里easy支付-->
        <alipay.easysdk.version>2.2.0</alipay.easysdk.version>
        <!--阿里通用支付-->
        <alipay-sdk-java.version>4.38.61.ALL</alipay-sdk-java.version>
        <!--微信支付-->
        <wechatpay.version>0.4.8</wechatpay.version>
        <!--guava版本 -->
        <guava.version>23.0</guava.version>
        <!--fastjson版本-->
        <fastjson.version>1.2.73</fastjson.version>
        <!--sharding-jdbc版本-->
        <sharding-jdbc.version>4.1.1</sharding-jdbc.version>
        <!--redisson版本-->
        <redisson-spring-boot>3.11.2</redisson-spring-boot>
        <!--hessian协议支持-->
        <hessian.version>4.0.7</hessian.version>
        <!--xxl-job-->
        <xxl-job-core.version>2.1.2</xxl-job-core.version>
        <!--阿里当前线程，解决父子线程间参数传递问题-->
        <transmittable.version>2.12.2</transmittable.version>
        <!-- 七牛 sdk 版本 -->
        <qiniu.version>7.7.0</qiniu.version>
        <!-- mysql数据库版本 -->
        <mysql.version>8.0.19</mysql.version>
    </properties>

    <!--声明jar-->
    <dependencyManagement>
        <dependencies>
            <!--基础模块-工具包-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-commons</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-redis支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-redis</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!-- 外部数据源 -->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-out-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!-- 数据字典 -->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>dict-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!-- ai模块 -->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-ai</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!-- rule模块 -->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-rule-base</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-knife4j-web支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-knife4j-web</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>sfbx-apache-httpclient</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-knife4j-gateway支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-knife4j-gateway</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-gateway支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-gateway</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-web支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-web</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-feign支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-feign</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-seata支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-seata</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-rabbitmq支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-rabbitmq</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-mybatis支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-mybatis-plus</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-task-executor支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-task-executor</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-xxl-job支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-xxl-job</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-influxdb支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>framework-influxdb</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-权限接口支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>security-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>rule-client</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-文件接口支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>file-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-短信接口支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>sms-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-数据埋点支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>points-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-交易模块支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>trade-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--保险模块-service支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>insurance-service</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!--基础模块-保险模块支持-->
            <dependency>
                <groupId>com.itheima.sfbx</groupId>
                <artifactId>insurance-interface</artifactId>
                <version>${sfbx-cloud.version}</version>
            </dependency>
            <!---springcould主配置-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!---spring-cloud-alibaba主配置-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!---springboot主配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- sharding-jdbc -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <!-- 使用XA事务时，需要引入此模块 -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-transaction-xa-core</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <!-- 使用BASE事务时，需要引入此模块 -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-transaction-base-seata-at</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <!--工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!--knife4j版接口文档 访问/doc.html -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--Mysql支持-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!--druid的springboot配置-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--springboot关于mybatis-plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <!--代码生成器模板引擎 相关依赖-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <!-- 阿里easy支付 -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-easysdk</artifactId>
                <version>${alipay.easysdk.version}</version>
            </dependency>
            <!-- 阿里通用版本支付- -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay-sdk-java.version}</version>
            </dependency>
            <!-- 微信支付 -->
            <dependency>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <artifactId>wechatpay-apache-httpclient</artifactId>
                <version>${wechatpay.version}</version>
            </dependency>
            <!--redis缓存客户端-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot}</version>
            </dependency>
            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <!--使用spring-cloud-starter-stream对rabbitmq的支持-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
                <version>${stream-rabbit.version}</version>
            </dependency>
            <!--百度云短信-->
            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>bce-java-sdk</artifactId>
                <version>0.10.119</version>
            </dependency>
            <!--阿里云短信-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>2.0.24</version>
            </dependency>
            <!--腾讯云短信-->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>3.1.270</version>
            </dependency>
            <!-- 阿里当前线程，解决父子线程间参数传递问题 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.version}</version>
            </dependency>
            <!-- 七牛 sdk  -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>
            <!---spring-cloud-alibaba-oss配置-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alicloud-oss</artifactId>
                <version>2.2.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.10.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--jdk插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>

            <!--springboot的打包插件-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.0.RELEASE</version>
            </plugin>

            <!-- maven-surefire-plugin 测试包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.4.2</version>
                <configuration>
                    <!-- 全局是否执行maven生命周期中的测试：是否跳过测试 -->
                    <skipTests>true</skipTests>
                    <!-- 解决测试中文乱码-->
                    <forkMode>once</forkMode>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                </configuration>
            </plugin>

        </plugins>
    </build>
</project>
